<template>
  <vxe-table
    :header-cell-config="{ height: 34, ...props.headerCellConfig }"
    :row-config="{ isCurrent: true, isHover: true, ...props.rowConfig }"
    :cell-config="{ height: 34, ...props.cellConfig }"
    :id="props.id"
    :data="props.data"
    :height="props.height"
    :custom-config="vxeCustomConfig"
    stripe
    border
    show-overflow
    ref="tableRef"
  >
    <slot name="table"></slot>
  </vxe-table>
</template>

<script lang="ts" setup>
import { vxeCustomConfig } from '@/utils/vxeHelper'
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  headerCellConfig: propTypes.object.def({}),
  rowConfig: propTypes.object.def({}),
  cellConfig: propTypes.object.def({}),
  id: propTypes.string.def('vxe-table'),
  data: propTypes.oneOf<any[]>([]).def([]),
  height: propTypes.oneOf<string | number>([]).def(200)
})

const tableRef = ref()
/** 连接表头操作栏与表格 */
function connect(toolbarRef: any) {
  if (unref(tableRef) && toolbarRef) {
    unref(tableRef).connect(toolbarRef)
  }
}

defineExpose({
  connect
})
</script>
