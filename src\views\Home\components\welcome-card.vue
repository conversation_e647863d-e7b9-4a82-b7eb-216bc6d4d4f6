<template>
  <div class="flex items-center bg-#fff p-10px rounded-5px">
    <el-avatar :src="avatar" :size="60" class="mr-16px">
      <img src="@/assets/imgs/avatar.gif" alt="" />
    </el-avatar>
    <div>
      <div class="text-20px"> {{ username }}, {{ t('workplace.welcome') }}! </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'

const { t } = useI18n()
const userStore = useUserStore()
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
console.log(userStore.getUser)
</script>
