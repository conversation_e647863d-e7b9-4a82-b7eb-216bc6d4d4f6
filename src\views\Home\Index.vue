<template>
  <div :class="mobile ? 'grid-layout-mobile' : 'grid-layout'">
    <!-- Item slot usage -->
    <!-- <GridLayout
      v-model:layout="layout"
      :col-num="12"
      :row-height="30"
      is-draggable
      is-resizable
      vertical-compact
      use-css-transforms
      responsive
    >
      <GridItem
        v-for="item in layout"
        :key="item.i"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        :static="item?.static"
        :class="{ 'overflow-y-auto': item?.static }"
      >
        
      </GridItem>
    </GridLayout> -->
    <div v-for="item in layout" :key="item.i" class="layout-item">
      <component :is="componentMap[item.componentName]" :data="item?.data" :name="item.name" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import welcomeCard from './components/welcome-card.vue'
import shortcutCard from './components/shortcut-card.vue'
import { reactive } from 'vue'
import { usePermissionStore } from '@/store/modules/permission'
import { useAppStore } from '@/store/modules/app'
const permissionStore = usePermissionStore()
const { mobile } = useAppStore()

const exists = reactive([
  'Redirect',
  'Home',
  'UserInfo',
  'dict',
  'CodegenEdit',
  'JobL',
  'Login',
  'SSOLogin',
  'SocialLogin',
  'NoAccess',
  'NoFound',
  'Error',
  'bpm',
  '/system',
  '/infra',
  '/report'
])

// 递归函数，获取最深层级的路由
function getDeepestRoutes(
  routes: any[],
  parentName: string = '',
  currentDepth: number = 0,
  maxDepth: number = 0,
  deepestRoutes: any[] = []
) {
  routes.forEach((route) => {
    if (route.children && route.children.length > 0) {
      getDeepestRoutes(
        route.children,
        route?.meta?.title,
        currentDepth + 1,
        maxDepth,
        deepestRoutes
      )
    } else {
      if (currentDepth >= maxDepth) {
        route.meta.parentName = parentName
        deepestRoutes.push(route)
      }
    }
  })
  return deepestRoutes
}

// 定义组件映射
const componentMap = {
  welcomeCard,
  shortcutCard
}

// 定义布局
const layout = reactive<any[]>([
  { x: 9, y: 0, w: 3, h: 3, i: '0', componentName: 'welcomeCard', data: undefined }
])

onMounted(() => {
  let menu = permissionStore.getRouters.filter(
    (router) => !exists.includes(router.name) && router.name
  )
  // 按照 route.meta.parentName 进行分组
  const menuGroup = getDeepestRoutes(menu)?.reduce((acc, route) => {
    const parentName = route.meta?.parentName || '未分组'
    if (!acc[parentName]) {
      acc[parentName] = []
    }
    acc[parentName].push(route)
    return acc
  }, {})
  let i = 0
  for (let key in menuGroup) {
    layout.push({
      x: 0,
      y: i++ * 5,
      w: 9,
      h: 5,
      i: i + '',
      componentName: 'shortcutCard',
      static: true,
      data: menuGroup[key],
      name: key
    })
  }
})
</script>

<style scoped lang="scss">
.vgl-layout {
  --vgl-placeholder-bg: red;
  --vgl-placeholder-opacity: 20%;
  --vgl-placeholder-z-index: 2;

  --vgl-item-resizing-z-index: 3;
  --vgl-item-resizing-opacity: 60%;
  --vgl-item-dragging-z-index: 3;
  --vgl-item-dragging-opacity: 100%;

  --vgl-resizer-size: 10px;
  --vgl-resizer-border-color: #444;
  --vgl-resizer-border-width: 2px;
}
.vgl-item--placeholder {
  z-index: var(--vgl-placeholder-z-index, 2);
  user-select: none;
  background-color: var(--vgl-placeholder-bg, red);
  opacity: var(--vgl-placeholder-opacity, 20%);
  transition-duration: 100ms;
}
.vgl-layout {
  --vgl-placeholder-bg: green;
}
// .vgl-layout::before {
//   position: absolute;
//   width: calc(100% - 5px);
//   height: calc(100% - 5px);
//   margin: 5px;
//   content: '';
//   background-image:
//     linear-gradient(to right, lightgrey 1px, transparent 1px),
//     linear-gradient(to bottom, lightgrey 1px, transparent 1px);
//   background-repeat: repeat;
//   background-size: calc(calc(100% - 5px) / 12) 40px;
// }

.vgl-item {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 10px;
}

//

.grid-layout {
  column-count: 2;
  column-gap: 10px;
  break-inside: avoid;
  .layout-item {
    break-inside: avoid;
    margin-bottom: 10px;
  }
}

.grid-layout-mobile {
  .layout-item + .layout-item {
    margin-top: 10px;
  }
}
</style>
