<template>
  <el-card shadow="never">
    <template #header>
      <card-title :title="props.name" />
    </template>
    <div class="flex items-top justify-start flex-wrap gap-10px">
      <el-tooltip
        :content="router.meta?.title"
        placement="top"
        v-for="(router, index) in props.data"
        :key="index"
      >
        <div class="shortcut" @click="push({ name: router.name })">
          <div class="shortcut-icon">
            <Icon :icon="router.meta?.icon" :size="30" color="#fff" />
          </div>

          <div class="ml-2px shortcut-text">
            {{ router.meta?.title }}
          </div>
        </div>
      </el-tooltip>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'

const { push } = useRouter()

const props = defineProps({
  data: propTypes.oneOf<any[]>([]).isRequired,
  name: propTypes.string.def('')
})
</script>

<style lang="scss" scoped>
.shortcut {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 70px;
  padding: 5px 5px;
  border-radius: 8px;
  text-align: center;
  color: var(--el-text-color-regular);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--el-bg-color-overlay);

  .shortcut-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background-color: var(--el-color-primary);
    flex-shrink: 0;
  }

  .shortcut-text {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
    max-height: 3.9em; // 3行高度限制
  }

  &:hover {
    background-color: var(--el-color-primary-light-9);
    // transform: translateY(-2px);
    // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

:deep(.el-card__header) {
  padding: 8px 20px !important;
}
:deep(.el-card__body) {
  padding: 5px 5px !important;
}
.el-card {
  border: none;
}
</style>
