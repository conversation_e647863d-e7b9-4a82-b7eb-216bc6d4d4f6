import * as echarts from 'echarts/core'

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>au<PERSON><PERSON>,
  <PERSON><PERSON>hart,
  MapChart,
  <PERSON>ctorialBar<PERSON>hart,
  <PERSON><PERSON><PERSON>,
  RadarChart
} from 'echarts/charts'

import {
  AriaComponent,
  DataZoomComponent,
  Grid<PERSON>omponent,
  LegendComponent,
  <PERSON>llelComponent,
  PolarComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent
} from 'echarts/components'

import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  DataZoomComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  VisualMapComponent,
  BarChart,
  LineChart,
  PieChart,
  MapChart,
  CanvasRenderer,
  PictorialBarChart,
  RadarChart,
  GaugeChart,
  FunnelChart
])

export default echarts
